import { redirect, RedirectType } from 'next/navigation'
import { NextRequest, NextResponse } from 'next/server'

import { checkIfUserBoughtAlready, startPaymentLog } from '@/actions/payment'
import { getProductById, PlatformsEnum, ProductStatus } from '@/actions/product'
import { createCheckoutSession } from '@/actions/stripe'
import { saveStripePaymentEvent } from '@/actions/stripe/saveStripePaymentEvent'
import { getUserById } from '@/actions/user'
import { HOST } from '@/config'
import { getFullAuthSession } from '@/io/next-auth-config'
import { getPlatform } from '@/utils/get-platform'
import { check, set } from '@/utils/jwt'
import logger from '@/utils/logger'

type Params = {
  params: Promise<{ id: string }>
}

/**
 * Listens
 */
export async function GET(request: NextRequest, { params }: Params) {
  const { id } = await params
  const session = await getFullAuthSession()
  if (!session?.user?.id) {
    logger.trace(`User not logged in: <${session?.user?.id}>`)
    return redirect(`${HOST}/login?redirect=${request.url}`)
  }

  const user = await getUserById(session.user.id)
  if (!user) {
    logger.trace(`User id not in sync: <${session?.user?.id}>`)
    return redirect(`${HOST}/login?redirect=${request.url}`)
  }

  // Onboarding is not required for now
  // if (user.onboardStatus !== OnboardStatus.ONBOARDED) {
  //   logger.trace(`User not onboarded: <${session?.user?.id}>`);
  //   return redirect(`${HOST}/onboard?redirect=${request.url}`);
  // }

  // opening url link
  const url = new URL(request.url)
  const urlExternalState = url.searchParams.get('externalState')
  const externalState = await check(urlExternalState)

  if (id !== externalState.productId) {
    return new NextResponse('Payment link is invalid', { status: 403 })
  }

  const product = await getProductById(
    externalState.productId,
    ProductStatus.PUBLISHED,
  )

  if (!product) {
    return new NextResponse(
      'Product not found, not published or not available',
      {
        status: 404,
      },
    )
  }

  if (product?.price.amount === 0 && externalState) {
    const state = await set({
      ...(externalState as Record<string, any>),
      viaBuyLink: true,
    })
    const redeemCompleteUrl = `${HOST}/redeem/${id}?externalState=${state}`
    return NextResponse.redirect(redeemCompleteUrl)
  }

  const alreadyBought = await checkIfUserBoughtAlready(
    session.user.id,
    product?.id ?? '',
  )

  if (alreadyBought) {
    logger.trace(
      `User already bought, <${session?.user?.id}> userId, <${product?.id}> productId`,
    )

    return redirect(
      `${HOST}/dashboard/purchases/#product/${product?.id.toString().split(':').pop()}`,
      RedirectType.replace,
    )
  }
  /** Login verification ends */

  /** Starting checkout */
  const isSubscription =
    getPlatform(product?.platforms) === PlatformsEnum.SUBSCRIPTION

  const platformFeePercentage = 0.2 // 20%

  const applicationFeeAmount = product?.price.amount
    ? Math.round(product.price.amount * platformFeePercentage)
    : 0

  const sellerStripeConnectId = product?.owner?.profile?.stripeConnectId

  const checkout = await createCheckoutSession(
    product?.price.externalId ?? '',
    isSubscription ? 'subscription' : 'payment',
    {
      productId: product?.id ?? '',
      buyerUserId: session.user.id,
      paymentPriceId: product?.price?.id.toString() ?? '',
      sellerUserId: product?.owner?.id.toString() ?? '',
      externalState: urlExternalState ?? '',
      mode: isSubscription ? 'subscription' : 'payment',
    },
    sellerStripeConnectId && applicationFeeAmount > 0
      ? sellerStripeConnectId
      : undefined,
    sellerStripeConnectId && applicationFeeAmount > 0
      ? applicationFeeAmount
      : undefined,
  )
  
  const paymentLog = await startPaymentLog(
    product?.owner?.id.toString() ?? '',
    session.user.id,
    product?.id ?? '',
    {
      productId: product?.id ?? '',
      buyerUserId: session.user.id,
      linkState: externalState ?? '',
    },
  )

  await saveStripePaymentEvent({
    type: 'stripe.dsm.checkout.init',
    objectId: checkout.id,
    eventId: checkout.id,
    data: JSON.stringify({
      productId: product?.id ?? '',
      buyerUserId: session.user.id,
      linkState: externalState ?? '',
      paymentLogId: paymentLog.id,
    }),
  })

  if (!checkout.url || !checkout.id) {
    return new NextResponse('Checkout not created, aborting', { status: 500 })
  }

  // checkout complete, redirecting
  return NextResponse.redirect(checkout.url, {
    status: 307,
    headers: {
      'Set-Cookie': `checkout_session=${checkout.id}; Path=/product/${product?.id.toString().split(':').pop()}; HttpOnly; Secure; SameSite=Strict`,
    },
  })
}
