'use client'

import * as React from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { createStripeConnectAccount } from '@/actions/stripe/connect'
import { useToast } from '@/hooks/use-toast'
import useSessionUser from '@/hooks/useSessionUser'
import useUser from '@/hooks/useUser'
import { CheckCircle2 } from 'lucide-react'

interface IntegrationsTabProps {
  className?: string
}

const IntegrationsTab: React.FC<IntegrationsTabProps> = ({ className }) => {
  const [isLoading, setIsLoading] = React.useState(false)
  const [isCreator, setIsCreator] = React.useState(false)

  const { toast } = useToast()
  const { data: userSession } = useSessionUser()
  const userId = userSession?.id ? String(userSession.id) : ''
  const { data: user } = useUser(userId)

  React.useEffect(() => {
    if (user && user.hats) {
      setIsCreator(user?.hats?.some(hat => hat.slug === 'creator'))
    }
  }, [user])

  const handleIntegrate = async () => {
    setIsLoading(true)

    try {
      const result = await createStripeConnectAccount()

      console.log('Create Stripe Connect Account Result:', result)

      if (result.url) {
        window.location.href = result.url
      } else {
        toast({
          title: 'Error',
          description: 'Failed to create Stripe connect link',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Error integrating with Stripe:', error)
      toast({
        title: 'Error',
        description: 'Error on integrate with Stripe',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }

  }

  return (
    <div className={cn('space-y-6', className)}>
      <Card>
        <CardContent className="p-0">
          {isCreator ? (
            <div className="space-y-5 p-6 tracking-wide">
              <h2 className="text-lg font-semibold">Payment integrations</h2>
              <Button
                onClick={handleIntegrate}
                disabled={isLoading || !!user?.profile?.stripeConnectId}
              >
                {isLoading ? (
                  'Loading...'
                ) : user?.profile?.stripeConnectId ? (
                  <>
                    Stripe Account Connected
                    <CheckCircle2 />
                  </>
                ) : (
                  'Connect Stripe Account'
                )}
              </Button>
            </div>
          ) : (
            <p className="text-muted-foreground text-center py-10 text-zinc-500 tracking-wide">
              Integrations settings coming soon
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default IntegrationsTab
